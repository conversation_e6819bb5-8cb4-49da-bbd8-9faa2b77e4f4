/*
 * @Date: 2024-02-27 11:30:55
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-27 11:33:26
 * @FilePath: /yanxuan-rpa-common-server/server/src/conf/config.base.ts
 * @Description: 
 * @Author: Lamwolf<PERSON> <EMAIL>
 */
import { IAppConfig, IPlugin, NodeEnv } from '@tiger/core';
import { ITigerProxyOption } from '@tiger/proxy';
import { join } from 'path';
import { AppModuleConfig, RedisConfigInterface } from './types';

const rootPath = join(__dirname, '../../');
const contextPath = '/base';
const xhrPrefix = '/xhr';
const productCode = 'olympus-pub';
const serviceCode = 'yanxuan-rpa-common-server';
const productId = 3001;
const appName = serviceCode;

export default abstract class BaseConfig implements IAppConfig {
  // 应用的名字
  appName: string = appName;
  // contextPath，一般表示应用域名的后面一位，比如,yx.mail.netease.com/ape，contextPath就是ape
  contextPath: string = contextPath;
  // 一般将接口进行前缀区分，一般是/xhr
  xhrPrefix: string = xhrPrefix;
  // 应用研发工作台申请的productCode，申请地址：http://yx.mail.netease.com/ape/#/edit/project?from=new
  productCode: string = productCode;
  //  应用研发工作台申请的productId，申请地址：http://yx.mail.netease.com/ape/#/edit/project?from=new
  productId: number = productId;
  serviceCode: string = serviceCode;

  plugins: IPlugin[] = [];

  // 使用apollo配置中心的时候用
  // @Value('logPath', '/home')
  // logPath!: string;

  // 应用的根目录
  rootPath: string = rootPath;
  // 日志的目录
  abstract loggerPath: string;
  // 服务端模版目录
  viewPath: string = join(rootPath, 'views');
  // 前端index.html,ico,fonts等资源目录
  webAppPath: string = join(rootPath, 'web/app');
  // 前端静态资源的存放目录
  webStaticPath: string = join(rootPath, 'web/mimg');
  // server监听端口
  port: number = process.env.consul_service_port ? Number(process.env.consul_service_port) : 8080;
  // 环境
  env: string = NodeEnv.Current.env.code;

  accessKeyId: string = 'jnrWsX6efU4gYyP9@platform';
  accessKeySecret: string = 'p6dK0ZTXsxWnM7tADQvau4N19gmGHRzr';

  domain: string = '';
  // app自己的转发配置，需要开发自己改成自己应用的 TODO
  abstract appProxyOptions: ITigerProxyOption;
  // 权限中心的转发配置
  abstract umcProxyOptions: ITigerProxyOption;
  // 消息中心的转发配置
  abstract msgProxyOptions: ITigerProxyOption;
  // 消息中心的转发配置
  abstract uasProxyOptions: ITigerProxyOption;
  // rpa后端服务的转发到pop服务配置
  abstract popProxyOptions: ITigerProxyOption;
  // rpa后端服务的转发到distribution服务配置
  abstract distributionOptions: ITigerProxyOption;
  // nos上传服务的转发配置（仅用于测试nos服务）
  abstract nosUploadProxyOptions: ITigerProxyOption;
  // 短视频服务
  abstract videoProxyOptions: ITigerProxyOption;
  // 拼多多服务
  abstract pddProxyOptions: ITigerProxyOption;

  // kfadmin服务的转发配置
  abstract kfadminOptions: ITigerProxyOption;
  // nlp服务的转发配置（仅用于测试nos服务）
  abstract nlpProxyOptions: ITigerProxyOption;
  // rpa业务服务的转发
  abstract rpaBusinessProxyOptions: ITigerProxyOption;
  // yanxuan-order-query服务的转发
  abstract yanxuanOrderQueryProxyOptions: ITigerProxyOption;
  // yanxuan-static-service 服务的转发
  abstract yanxuanStaticServiceProxyOptions: ITigerProxyOption;
  // sylas服务转发
  abstract commentServiceProxyOptions: ITigerProxyOption;
  // sv服务转发
  abstract svProxyOptions: ITigerProxyOption;
  abstract waiterProxyOptions: ITigerProxyOption;
  // ius服务转发
  abstract iusProxyOptions: ITigerProxyOption;
  // 数据库配置
  dbConfig: any;
  // Redis配置
  abstract redisConfig: RedisConfigInterface;
  // 外部模块配置
  modules: AppModuleConfig = {
    '@tiger/security': {
      enable: true,
      options: {
        csrf: true,
        'Strict-Transport-Security': true,
        'X-Frame-Options': true
      }
    },
    '@tiger/swagger': {
      enable: true,
      options: {
        appModule: join(__dirname, '../modules/index.ts'),
        swaggerDefinition: {
          host: 'local.yx.mail.netease.com'
        }
      }
    }
  };
}
