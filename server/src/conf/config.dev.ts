/*
 * @Date: 2024-01-03 14:37:43
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-27 11:34:01
 * @FilePath: /yanxuan-rpa-common-server/server/src/conf/config.dev.ts
 * @Description: 
 * @Author: Lamwolf<PERSON> <EMAIL>
 */
import { ITigerProxyOption } from '@tiger/proxy';
import BaseConfig from './config.base';
import { join } from 'path';

// @EnableApolloConfig('application') 这个注解给apollo配置中心的时候用
export default class Config extends BaseConfig {
  domain: string = '';
  loggerPath: string = join(this.rootPath, 'logs');
  appProxyOptions: ITigerProxyOption = {
    target: 'http://test.yx.mail.netease.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };
  msgProxyOptions: ITigerProxyOption = {
    target: 'http://rpa.test.you.163.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };
  nosUploadProxyOptions: ITigerProxyOption = {
    target: 'http://rpa.test.you.163.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };
  uasProxyOptions: ITigerProxyOption = {
    target: 'http://rpa.test.you.163.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };
  popProxyOptions: ITigerProxyOption = {
    target: 'http://rpa.test.you.163.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };
  distributionOptions: ITigerProxyOption = {
    target: 'http://rpa.test.you.163.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };
  kfadminOptions: ITigerProxyOption = {
    target: 'http://rpa.test.you.163.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };
  nlpProxyOptions: ITigerProxyOption = {
    target: 'http://rpa.test.you.163.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };
  rpaBusinessProxyOptions: ITigerProxyOption = {
    target: 'http://rpa.test.you.163.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };
  yanxuanOrderQueryProxyOptions: ITigerProxyOption = {
    target: 'http://rpa.test.you.163.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };
  yanxuanStaticServiceProxyOptions: ITigerProxyOption = {
    target: 'http://rpa.test.you.163.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };
  umcProxyOptions: ITigerProxyOption = {
    target: 'http://************',
    changeOrigin: true,
    autoRewrite: true,
    headers: { Host: 'yxius.you.163.com' },
    rewrite: (path: string) => {
      return path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}/userCenterManage`
        ),
        ''
      );
    }
  };
  videoProxyOptions: ITigerProxyOption = {
    target: 'http://rpa.test.you.163.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };
  pddProxyOptions: ITigerProxyOption = {
    target: 'http://rpa.test.you.163.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };
  commentServiceProxyOptions: ITigerProxyOption = {
    target: 'http://rpa.test.you.163.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };
  svProxyOptions: ITigerProxyOption = {
    target: 'http://rpa.test.you.163.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };
  
  waiterProxyOptions: ITigerProxyOption = {
    target: 'http://rpa.test.you.163.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };

  iusProxyOptions: ITigerProxyOption = {
    target: 'http://rpa.test.you.163.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };

  // Redis配置
  redisConfig = {
    host: 'localhost',
    port: 6379
  };
}
