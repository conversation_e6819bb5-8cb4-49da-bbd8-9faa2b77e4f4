import { BaseApplication, TgApp } from '@tiger/boot';
import { loadEagleMiddleware } from '../middlewares/global.middleware';
import { AppModule } from '../modules';
import Application from 'koa';
import * as config from '../conf';
import { appLogger } from '@tiger/logger';
import ProxyRoutes from '../proxy'
import swagger from '@tiger/swagger';
import aixos from '@tiger/request';

@TgApp
class MainApplication extends BaseApplication {
  public async beforeLoadRoutes(app: Application) {
    // 引入常用的全局app middleware
    loadEagleMiddleware(app);
  }

  public async afterLoadRoutes(app: Application) {
    // swagger 生成
    app.use(swagger());
  }
}

async function bootstrap() {
  const app = await new MainApplication().start<AppModule>(
    AppModule.getInstance()
  );
  app.listen(config.port).addListener('listening', () => {
    appLogger.info('server is started on port: ' + config.port);
    // 注册sacs
    aixos.post(
      `http://127.0.0.1:8550/proxy/${config.env}.yanxuan-sacs.service.mailsaas/sacs/register`,
      {
        contextPath: '/',
        serviceCode: config.serviceCode,
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    ).then(res => {
      appLogger.info(`SACS注册结果：${JSON.stringify(res.data)}`);
    }).catch(err => {
      appLogger.error(`SACS注册失败：${err}`);
    })
  });
}

bootstrap();
