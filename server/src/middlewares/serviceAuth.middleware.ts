import { Context } from "koa";
import { getApiServiceList } from "../apolloy";
import { AjaxResult } from "@tiger/core";
import { appLogger } from '@tiger/logger';
import aixos from '@tiger/request';
import config from '../conf';

export default async function serviceAuth(ctx: Context, next: any) {
  const { headers } = ctx.request;
  const consulHeader = ctx.request.get('NTES-CNGINX-HOST');
  const serviceTokenHeader = ctx.request.get('yx-service-token');

  if (!consulHeader && !serviceTokenHeader) {
    appLogger.error(`服务鉴权失败，缺少头信息：${JSON.stringify(headers)}`);
    ctx.body = AjaxResult.unauthorized('You are forbidden');
    return;
  }

  let serviceCode = '';

  if (consulHeader) {
    serviceCode = consulHeader.split('.')[0]
    const serviceList = getApiServiceList()
    appLogger.info(
      `serviceCode: ${serviceCode} serviceList: ${JSON.stringify(serviceList)}`
    )

    if (!serviceList.includes(serviceCode)) {
      ctx.body = AjaxResult.unauthorized(
        `Service:${serviceCode} is not permitted`
      )
      return
    }
  } else if (serviceTokenHeader) {
    const { data } = await aixos.post(
      `http://127.0.0.1:8550/proxy/${config.env}.yanxuan-sacs.service.mailsaas/sacs/authentication`,
      {
        env: config.env.toUpperCase(),
        token: serviceTokenHeader,
        serviceCode: config.serviceCode,
        path: ctx.request.path,
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
    appLogger.info(
      `SACS鉴权结果：${JSON.stringify(data)}`
    )
    if (!data.data.result) {
      ctx.body = AjaxResult.unauthorized(
        `Service:${serviceCode} is not permitted`
      )
      return
    }
  }

  await next();
}