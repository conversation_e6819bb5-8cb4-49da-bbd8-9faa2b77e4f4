/*
 * @Date: 2024-01-03 15:42:34
 * @LastEditors: <PERSON>wolf<PERSON> <EMAIL>
 * @LastEditTime: 2024-02-04 10:03:08
 * @FilePath: /yanxuan-rpa-common-server/server/src/modules/xhr/code/vo/inputTrigger.vo.ts
 * @Description: 
 * @Author: Lamwolf<PERSON> <EMAIL>
 */
export class InputTriggerVO {
  /** 短信内容 */
  message: string;
  /** 手机号 */
  phone: number;
  /** 本次填写的唯一key，用于后续查询，建议基于时间戳生成 */
  key: string;
}
export class GetInputVO {
  /** 本次填写的唯一key，用于后续查询，建议基于时间戳生成 */
  key: string;
}

export class CodeSubmitVO {
  /** 本次填写的唯一key，用于后续查询，建议基于时间戳生成 */
  key: string;
  /** 验证码 */
  code: string;
}

export class InputInnerTriggerVO {
  /** 用户id */
  uids: string[];
  /** 接收短信的手机号 */
  phone: number;
  /** 本次填写的唯一key，用于后续查询，建议基于时间戳生成唯一key */
  key: string;
  /** 消息通知的方式 'popo' | 'email' | 'feishu' */
  msgMethod: ('popo' | 'email' | 'feishu')[];
  /** 消息通知内容模版 */
  msgTemplate: string;
}

export class SendMessageInfoVO {
  /** 接收短信的手机号 */
  phone: string;
  /** 接收到的短信内容 */
  text: MessageInfoVO;
  /** 消息类型 */
  msgtype: 'text' | 'image' | 'voice';
}

export class MessageInfoVO {
  /** 接收到的短信内容 */
  content: string;
}

export class GetCodeVO {
  /** 触发验证码时间戳 */
  tiggerTime: number;
  /** 电话号码 */
  phone: string;
  /** 渠道 */
  channel: string;
}

export class TiggerCodeCheckVO { 
    /** 电话号码 */
    phone: string;
    /** 渠道 */
    channel: string;
}

export class GetCodeByWaitKeyVO{
  /** 等待验证码的key */
  waitKey: string;
}