/*
 * @Date: 2024-01-11 15:36:47
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-27 11:35:21
 * @FilePath: /yanxuan-rpa-common-server/server/src/proxy/proxy.common.ts
 * @Description: 
 * @Author: Lamwolf<PERSON> <EMAIL>
 */
/**
 * @description
 * 此文件主要用于配置基础的转发代理服务
 */
import proxy from '@tiger/proxy';
import Router from 'koa-router';
import * as config from '../conf';

const router = new Router({
  prefix: config.contextPath
});

// 权限中心的转发配置
router.all('/xhr/userCenterManage/*', proxy('*', config.umcProxyOptions));

router.all('/xhr/msg/*', proxy('*', config.msgProxyOptions));
router.all('/xhr/uas/*', proxy('*', config.uasProxyOptions));
router.all('/xhr/rpaOpen/pop/*', proxy('*', config.popProxyOptions));
router.all('/xhr/rpaOpen/distribution/*', proxy('*', config.distributionOptions));
router.all('/xhr/rpaOpen/video/*', proxy('*', config.videoProxyOptions));
router.all('/xhr/rpaOpen/kfadmin/*', proxy('*', config.kfadminOptions));
router.all('/xhr/rpaOpen/business/*', proxy('*', config.rpaBusinessProxyOptions));
router.all('/xhr/rpaOpen/yanxuanOrderQuery/*', proxy('*', config.yanxuanOrderQueryProxyOptions));
router.all('/xhr/rpaOpen/yanxuanStatic/*', proxy('*', config.yanxuanStaticServiceProxyOptions));
router.all('/xhr/rpaOpen/pdd/*', proxy('*', config.pddProxyOptions));
router.all('/xhr/rpaOpen/sv/*', proxy('*', config.svProxyOptions));
router.all('/xhr/sv/*', proxy('*', config.svProxyOptions))
router.all('/xhr/rpaOpen/waiter/*', proxy('*', config.waiterProxyOptions));
router.all('/xhr/rpaOpen/ius/*', proxy('*', config.iusProxyOptions))

// 转发拼多多、抖音评论拉取服务
router.all('/xhr/rpaOpen/comment/*', proxy('*', config.commentServiceProxyOptions));

// 配置需要直接转发的应用模块 
router.all('/xhr/nlp/*', proxy('*', config.nlpProxyOptions));

const middleware = router.routes();
export default middleware;
