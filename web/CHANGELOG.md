## 0.0.1 (2025-07-29)


### Features

* add api comment ([6363171](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/6363171621199dcd97f6add3b2a205e69a769c1e))
* add apolloy config ([02d27f9](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/02d27f91809100b2ea2f802bf2a6c5636aafcc85))
* add bee swagger ([3648ea3](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/3648ea3e155da4c44d88d8eabc6ed4f1db6f8f75))
* add code input page ([cd6f5ed](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/cd6f5ed066036cf91fed2f6963087c986bc6b724))
* add comment for new middleware ([fcc91a7](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/fcc91a78bab666d21623d104e70fb5f35662515c))
* add formatSendTime func ([a3a32d8](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/a3a32d8276882d0182a3ee091e46854ecf89f9d1))
* add middleware ([70e1f42](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/70e1f421e78696f16ba007235798dcb158ecd937))
* add missing config ([6efe1f1](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/6efe1f14dc9b4b7e421935165e19f54eb1556c5c))
* add more logger ([b0558ad](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/b0558ad338e927601f075844361df3903fb6f425))
* add more logger ([d8059a6](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/d8059a669e25a0957a1c2b110b15bd684d63859a))
* add new api for get all shops ([204096f](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/204096f03503f41f09d68e5467bd663b43092f49))
* add new api for trigger code input multiply ([82673e6](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/82673e6803b37ff898a172f44bf76662531cd3e9))
* add new apolloy field ([6241cdc](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/6241cdc48ccc8c23949729995e1d627edfbb7cb7))
* add new proxy with sign validate ([237f3ab](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/237f3ab0675398b61dab4545d5cda2e8c7dacbfd))
* add nos upload ([17fab15](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/17fab159165f08ee126d7536707cd5afc3d76688))
* add nos upload module ([605602c](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/605602cf178886d197570570754d07bd24597da1))
* add phone format func ([a90b0ae](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/a90b0aeca4885f2e76e164702575ddfcdb17fe40))
* add proxy for receiveFromRobot ([b1641c5](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/b1641c5c8a7312da20a06f23ddfa2cf07ef8d7b3))
* add proxy logger ([c8f2a76](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/c8f2a7696af89f8e76c6c0f93cdc24edd78cebf5))
* add requestTime for sign ([a993382](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/a9933822970ec2f2fe2eb4bc2bfae237e127e41c))
* add set code api ([9848f49](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/9848f4987438f53408d7923d31d05d8b0dfb59b9))
* add sign valid success logger ([8e5abcf](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/8e5abcf0ea469280031fe7bb6228aa4b99d25a76))
* add swagger sheel ([f71a2a3](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/f71a2a3b139d3f9523d067a846f6955abae09410))
* add symAuth middleware ([c6876ea](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/c6876ea71fa32a83bff4b3fd4bfcaee0c092a193))
* add test console ([e93ab2e](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/e93ab2e84f29d3d4b2e832bd10fe0d80caa1140a))
* add two api ([3e2af49](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/3e2af49130c9d710ee3c282f5f6599b364936bed))
* auto ci/cd ([f8092b4](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/f8092b4a91857baf826924f0cd292a6aaa5215e5))
* change getter of shop info ([0bc12a7](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/0bc12a71b282b1951088b2e81d4aac992bfbe69e))
* compatibility format 2025-01-01 11:11:11 ([e8236b2](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/e8236b2c8bf9a90d4d41a8e37927bd02d486a54c))
* control page scale ([fc827b2](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/fc827b24da87b63242b2cea4f9ec5b15282b5494))
* decode sign for python & js ([8ece5c6](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/8ece5c693da2c218f46241a04de94f29aaa00d01))
* fix basic getShopInfo api ([cfb9414](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/cfb9414f82b984ab4a2b46f0be223e025e6517ef))
* fix build bug ([911298f](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/911298f0e7987544b25ad940caa2f5afee834bf9))
* fix mobile msg ([0d709e4](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/0d709e4d18855b2956079ecc7cb0d208af7723af))
* fix mobile msg service ([7f39bbd](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/7f39bbdb43f35c083873ce00982ed97a3039ec0e))
* fix request ([b463ec4](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/b463ec4f1a5419d08fad60f5f760cf25f12d62c3))
* fix sms calling ([99fefab](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/99fefab425209e0d091acd769df848605d65e227))
* fix some bug ([d5943e2](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/d5943e27f538edc3c1a71643546a4764c578fe1b))
* import code module ([ce1db04](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/ce1db043b354ffbfc26c9abd5f902feea5be75b1))
* init new api for channels ([5ac5c3c](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/5ac5c3c2340831fc28e7b0988758f3735fa27696))
* init project ([0a22745](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/0a22745b83e5c6d4d8c63ae1f2c2c56097c4ff70))
* modify clusterId ([b51d526](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/b51d52616e784c09b0106e9874087471d8a35dea))
* modify path of api ([78e2458](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/78e2458be3face790f5bb810bef45316e2b7f10a))
* move bodyParser forward ([11db5e6](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/11db5e60dcf6c70bfc7165732a6aca1252b122c9))
* remove ldcCode ([81e9825](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/81e982556cdd7bbb33a6eb90e8a350b891684ec8))
* remove openid ([7cf7a03](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/7cf7a03279802c859aac7ea06692c3ec653ea92b))
* remove upload image ([33705ca](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/33705ca218396fabbf9bb5164f558a2971f6fe9e))
* revert ldcCode ([1ad2b15](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/1ad2b15e7655e4bab737b8abd64a17cab33ab56d))
* switch to uas server ([edb5ef8](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/edb5ef88bef3ed3d5ae9378d437e1fd3afb8fb9a))
* trigger swagger ([d07cbc2](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/d07cbc2c0542c018d6ab43b029126cf25d396a5a))
* turn off autoDeploy ([95d1ec5](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/95d1ec55389fcc67fcb1c8f51bb38144afb361fb))
* 代码更新 ([676837f](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/676837f1394fa2077c9de947e528687a1ee37c0a))
* 代码更新 ([7d90d3f](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/7d90d3f29ee1457ccd98cd3da540b645a6c0030d))
* 修改 ([06e9b72](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/06e9b721e52a5d844d76890877f2d58253121190))
* 修改上传图片代码 ([117b636](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/117b636fceacf2de5a6caa22e51ee808c6c87b31))
* 修改代码逻辑 ([24b6726](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/24b672657a3cda180b04e26df722b6d01a5f9fbf))
* 修改代码逻辑 ([55d8c8d](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/55d8c8d7a205b14b943fdf656fe62cbdf3a70583))
* 修改代码逻辑 ([cc0db24](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/cc0db24c64236cf9bd941eea26d033d77ff5ee67))
* 修改代码逻辑 ([39d9c45](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/39d9c455db80794e0a51ea7e18a863f5e19fb081))
* 修改全局变量策略 ([924ddb9](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/924ddb917fb12926d726b7f88bcd59c21f5d769f))
* 修改全局变量策略 ([1be12b7](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/1be12b7a616eed54ab23fc51b2d357f001520f5f))
* 修改全局变量策略 ([0153b95](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/0153b954906f8b397a0be731d6cf90e833d127fb))
* 修改服务部署配置 ([a138cd3](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/a138cd34fe2d116bc54393945e3a301a0a242080))
* 修改线上环境转发配置 ([adc341e](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/adc341e20ba12af490e43382711d94aafe20afe5))
* 修改线上环境转发配置 ([a02d0ab](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/a02d0ab63aca0996f88f8534b1755ebc0916145a))
* 修改线上环境配置 ([b830628](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/b8306280c928cecfdd49dbeb208eb5320f4de02e))
* 修改转发服务 ([66fb70b](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/66fb70be4fac0022d33c56e0ddf3a6419e6aad13))
* 修改转发配置 ([38bb3bc](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/38bb3bc9bd32fc59c61afdad76f5f45fae9084e3))
* 修改错误提示 ([f829a40](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/f829a40786aac3e6a53c51cdc38408cb145d4d13))
* 修改验证码等待时长 ([77040ad](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/77040ad4c08d4dd4069c2377cd888d83c300bb07))
* 升级文件包 ([65782f0](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/65782f07bab950efb38ea2f04dd64c8f3637b93c))
* 增加Gpt日志统计 ([c662ae3](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/c662ae3acec06a1d8f7b958fe18bd69e1ac92c22))
* 定义全局变量 ([2e0287a](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/2e0287ad5640fa7b0f7698f839c0fc746ddfa965))
* 客服工单进度通知服务 ([d5bceba](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/d5bcebac2e4678951729d477a5ac0511597bb431))
* 打日志 ([9583551](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/9583551f9a7f0132c30eb535bbb79163c5143c5d))
* 控制进程数量 ([3b8bdbd](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/3b8bdbd74ffa69a848bad4e793876ec9b665b12e))
* 支持跨域访问 ([71fb4af](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/71fb4af24c280c742ba8b446a274dc189ef8eb51))
* 文件上传添加topic ([890c74d](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/890c74dafde4609f443253914f5bc1b8cb2f52ac))
* 新增gpt接口 ([6bea50c](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/6bea50c43ce8c6094baffdf049a748c262ad151e))
* 新增pdd服务转发配置 ([8b8eb5a](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/8b8eb5ae5635267759d20a2ce5dc8f4c5ca34ab8))
* 新增发送邮件接口、文件上传、下载接口 ([0fee4e1](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/0fee4e1411dd54545a4ec7e9e670c8c278075dd5))
* 新增发送邮件接口、文件上传、下载接口 ([e1252d7](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/e1252d72f1b79772ac8d126a10626cc7944edd80))
* 新增接口转发 ([d956a77](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/d956a77b93fa70f440874d33415d06691b06534f))
* 新增服务转发 ([fb9c0ec](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/fb9c0ec4cf10d945e79a73da76749638670abbe6))
* 测试gpt接口 ([c39e644](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/c39e644a3191d85f8ccc8a579b655ee27358ffd9))
* 测试修改 ([9becfd3](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/9becfd3d6b85333beba44c6773ce9253a30efc62))
* 测试改动 ([abe7751](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/abe775146195804c48adcfb0a3e2554dedcdf54d))
* 添加手动触法pdd任务接口 ([7b26b27](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/7b26b2717f56bd4f066468920b032cfb65ecfb46))
* 添加日志 ([35abad7](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/35abad772345070658f50c21bba55bef5c55d707))
* 添加日志 ([ac25c0a](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/ac25c0acc8924607f59353f57b34a8214ec890cb))
* 添加日志 ([6f0494a](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/6f0494af72fd41f3fb9ae95f22e08dd77b096e1e))
* 添加消息通知接口 ([48ca16a](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/48ca16a6904ee59d0dcf94fcb97a4907d9570e1c))
* 添加清除验证码逻辑 ([a1d017e](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/a1d017e616e9b697a93144fa2b7e76a51632b566))
* 添加清除验证码逻辑 ([c9d5301](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/c9d53012c07148be972d25866955b175b7ba1380))
* 添加获取pdd店铺信息接口 ([0e6ef60](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/0e6ef60881b8128db2d241a158fa1e8f67edcad9))
* 添加超时时间 ([c3cef17](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/c3cef17a4a384efca47ba9fc5ccd20938978a115))
* 添加鉴权 ([1659d7f](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/1659d7fb98e89efc8f278f221959aaceb6238407))
* 添加验证码逻辑 ([d07dc99](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/d07dc99c8273b91493a7f9a843ca8d10db9b3459))
* 给gpt接口添加鉴权校验 ([e0fb37b](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/e0fb37bd6940c1d7640346f8d0fe9998dea977d6))
* 解决代码合并冲突问题 ([dbf1814](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/dbf181491e7ab5b9645c3bf301db3e504f0f400f))
* 设置gpt网关超时时间 ([1e1e916](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/1e1e916663b185b5c9510bf3a6f9b07ad52a0900))
* 设置gpt网关超时时间 ([6b51b72](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/6b51b72d6b0a2c84288276a54b1260746e63d5d1))
* 设置接口超时时间 ([1f0e094](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/1f0e094536534a55530f5394458fe4c1fd8ee204))
* 调试接口 ([062d54c](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/062d54c4ced12335ab77e04ff06fbded68bc4a63))
* 调试接口 ([7b85d62](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/7b85d62e04f9ea6e7ce8484154fe6aa72b1f448a))
* 重写上传接口 ([0b0a785](https://git.yx.netease.com/yanxuan-fed/yanxuan-rpa-common-server/commits/0b0a785d36f96066918adb1209e52c899ce35c47))



